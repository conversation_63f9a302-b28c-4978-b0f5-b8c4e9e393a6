#pragma once
#include <cstddef>
#include <cstdint>
#include <string>
#include <Windows.h>
#include "../../imgui/imgui.h"

using std::string;

namespace globals {
  inline bool overlayVisible = false;
  inline bool insertPressed  = true;

  inline float colorlol[4]{ 0.f, 0.f, 0.f, 0.f };
  inline float colorlol2[4]{ 0.f, 0.f, 0.f, 1.0f };

  extern float offsetTexthealth;

  namespace Legitbot {
    inline bool enabled = false;
    inline bool teamcheck = false;
    inline bool visiblecheck = false;
    inline float smoothness = 1.f;
    inline float radius = 15.0f;

    namespace Circle {
      inline bool enabled = false;
      inline bool filled = false;
      inline ImVec4 Color = ImVec4( 1.0f, 1.0f, 1.0f, 0.5f );
    }
  }

  namespace Triggerbot {
    inline bool enabled = false;
    inline int hotkey = VK_LMENU;
  }

  namespace Screen {
    inline int width  = GetSystemMetrics( SM_CXSCREEN );
    inline int height = GetSystemMetrics( SM_CYSCREEN );
  }

  inline float centerX = ( Screen::width / 2 );
  inline float centerY = ( Screen::height / 2 );

  extern float window_width;
  extern float window_height;
  extern bool  isFirstFrameKeystrokes;

  extern float pos_x;
  extern float pos_y;

  namespace Keystrokes {
    inline bool enabled = false;
    inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 0.25f );
    inline ImVec4 pressedColor = ImVec4( 1.f, 1.f, 1.f, 1.f );
    inline float scale = 1.0f;
    inline float posX = 50.0f;
    inline float posY = 200.0f;
  }

  namespace DarkMode {
    inline bool enabled = false;
    inline float alpha = 50.0;
  }

  namespace Projectile {
    inline bool enabled = false;
    inline bool line = false;
    inline bool name = false;
    inline bool box = false;
    inline bool erase = false;
    inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
  }

  namespace Smoke {
    inline bool enabled = false;

    namespace circle {
      inline bool enabled = false;
      inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
    }

    namespace name {
      inline bool enabled = false;
      inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
    }

    namespace countdown {
      inline bool enabled = false;

      namespace bar {
        inline bool enabled = false;
        inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
      }

      namespace text {
        inline bool enabled = false;
        inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
      }
    }
  }

  namespace Crosshair {
    inline bool enabled = false;
    inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
    inline float thickness = 2.0;
    inline float length = 6.0;
    inline float gap = 3.0;
    inline bool dotenabled = false;
    inline float dotSize = 1.0;
  }

  inline bool isConsoleVisible = true;
  inline bool isRunning        = true;

  inline int  previousTotalHits = 0;
  inline bool isFirstRun        = true;

  namespace Sound {
    inline bool enabled = false;
    inline float volume = 0.0f;
  }

  namespace Hitsound {
    inline bool enabled = false;
    inline string sound = "yourfile.wav";
  }

  namespace Killsound {
    inline bool enabled = false;
    inline string sound = "yourfile.wav";
  }

  namespace HsKillsound {
    inline bool enabled = false;
    inline string sound = "yourfile.wav";
  }

  inline float flHitmarkerAlpha = 1.f;

  namespace Hitmarker {
    inline bool enabled = false;
    inline float gap = 3.0;
    inline float length = 6.0;
    inline float thickness = 2.0;
    inline float duration = 25.0;
    inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, flHitmarkerAlpha );  //  beachte das alpha dings bumbs
  }

  namespace MenuOutline {
    inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
  }

  namespace Misc {
    namespace RecoilCrosshair {
      inline bool enabled = false;
      inline float size = 3.0f;
      inline ImVec4 Color = ImVec4( 1.f, 0.f, 0.f, 1.f );
    }
  }

  namespace Watermark {
    inline bool enabled = false;
    inline bool showFPS = true;
    inline bool showRAM = true;
    inline bool showNebula = true;
    inline bool showBETA = true;
    inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
    inline ImVec4 gradientColor = ImVec4( 0.3f, 0.6f, 1.f, 1.f );
  }

  inline bool kernal   = true;
  inline bool external = false;

  namespace Esp {
    extern bool enabled;
    extern bool ignoreTeammates;

    // Health Bar Stile als Konstanten
    namespace HealthBarStyle {
      inline const int Reactive = 0;
      inline const int Solid = 1;
    }

    namespace Box {
      inline bool enabled = false;
      inline float length = 0.5;
      inline float rounding = 0.0;
      inline int type = 0;

      namespace Filled {
        inline bool enabled = false;
        inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 0.25f );
        inline ImVec4 Color2 = ImVec4( 1.f, 1.f, 1.f, 0.25f );
      }

      inline bool outline = false;
      inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );

      namespace Spotted {
        inline bool enabled = false;
        inline ImVec4 Color = ImVec4( 1.f, 0.f, 0.f, 1.f );
        inline ImVec4 drawingColor = ImVec4( 1.f, 1.f, 1.f, 1.f );
      }

      inline ImVec4 outlineColor = ImVec4( 1.f, 1.f, 1.f, 1.f );
    }

    namespace Health {
      namespace Bar {
        inline bool enabled = false;
        inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );

        namespace Style {
          inline int type = HealthBarStyle::Reactive;
        }

        namespace Glow {
          inline bool enabled = true;
        }
      }

      namespace Value {
        inline bool enabled = true;
        inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
      }
    }

    namespace Armor {
      namespace Bar {
        inline bool enabled = false;
        inline ImVec4 Color = ImVec4( 0.3f, 0.6f, 1.f, 1.f ); // Nice medium blue

        namespace Glow {
          inline bool enabled = true;
        }
      }

      namespace Value {
        inline bool enabled = true;
        inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
      }
    }

    namespace Snapline {
      inline bool enabled = false;
      inline float thickness = 1.0;
      inline string Start = "UPPER";

      namespace End {
        inline bool upper = true;
        inline bool head = false;
        inline bool center = false;
        inline bool lower = false;
      }

      inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );

      namespace Spotted {
        inline bool enabled = false;
        inline ImVec4 Color = ImVec4( 1.f, 0.f, 0.f, 1.f );
        inline ImVec4 drawingColor = ImVec4( 1.f, 1.f, 1.f, 1.f );
      }
    }

    namespace Skeleton {
      inline bool enabled = false;

      namespace Dots {
        inline bool enabled = false;
        inline float radius = 0.2;
        inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
      }

      namespace Head {
        inline bool enabled = false;
        inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
      }

      inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );

      namespace Spotted {
        inline bool enabled = false;
        inline ImVec4 Color = ImVec4( 1.f, 0.f, 0.f, 1.f );
        inline ImVec4 drawingColor = ImVec4( 1.f, 1.f, 1.f, 1.f );
      }
    }

    namespace Info {
      inline bool enabled = false;
      inline bool state = false;

      namespace Name {
        inline bool weapon = false;
        inline bool player = false;
      }

      namespace Icon {
        inline bool enabled = false;
      }

      inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );

      namespace Spotted {
        inline bool enabled = false;
        inline ImVec4 Color = ImVec4( 1.f, 0.f, 0.f, 1.f );
        inline ImVec4 drawingColor = ImVec4( 1.f, 1.f, 1.f, 1.f );
      }
    }

    namespace Viewline {
      inline bool enabled = false;
      inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f );
      inline float length = 10.0;

      namespace Spotted {
        inline bool enabled = false;
        inline ImVec4 Color = ImVec4( 1.f, 0.f, 0.f, 1.f );
        inline ImVec4 drawingColor = ImVec4( 1.f, 1.f, 1.f, 1.f );
      }

      namespace Dot {
        inline bool enabled = false;
        inline ImVec4 Color = ImVec4( 1.f, 0.f, 0.f, 1.f );
      }
    }

    namespace Death {
      inline bool enabled = false;
      inline float duration = 1.0f; // Duration in seconds (0.1s - 2.0s range)
      inline ImVec4 Color = ImVec4( 1.f, 1.f, 1.f, 1.f ); // Fixed color for death animations

      namespace Box {
        inline bool enabled = false;
      }

      namespace Skeleton {
        inline bool enabled = false;
      }

      namespace Info {
        inline bool enabled = false;
      }

      namespace Health {
        inline bool enabled = false;
      }

      namespace Armor {
        inline bool enabled = false;
      }

      namespace Snapline {
        inline bool enabled = false;
      }
    }

  }  // namespace Esp

  inline bool panicKey;

  extern string playerStateString;

  extern int HeightCfg;

  inline int UpperHeight  = Screen::height / Screen::height;
  inline int CenterHeight = Screen::height / 2;
  inline int LowerHeight  = Screen::height;
}  // namespace globals